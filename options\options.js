// FocusGuard Pro - Options Page JavaScript

class FocusGuardOptions {
  constructor() {
    this.blockedSites = [];
    this.settings = {};
    this.isEnabled = true;
    // Phase 4: Content filtering data
    this.contentFiltering = {};
    this.blockedPhrases = [];
    this.protectionDisabling = {};
    this.countdownTimer = null;
    // Phase 5: Schedule and redirect data
    this.scheduleSettings = {};
    this.smartRedirects = {};
    this.overrideTimer = null;
    this.init();
  }

  async init() {
    // Show loading
    this.showLoading(true);

    try {
      // Load data
      await this.loadData();

      // Setup navigation
      this.setupNavigation();

      // Setup event listeners
      this.setupEventListeners();

      // Update UI
      this.updateUI();

      // Hide loading
      this.showLoading(false);
    } catch (error) {
      console.error('Options initialization error:', error);
      this.showLoading(false);
    }
  }

  async loadData() {
    try {
      // Load blocked sites and additional data
      const response = await chrome.runtime.sendMessage({ action: 'getBlockedSites' });
      console.log('Options page received data:', response);
      if (response) {
        this.blockedSites = response.sites || [];
        this.isEnabled = response.isEnabled !== false;
        this.temporaryUnblocks = response.temporaryUnblocks || {};
        this.usageTracking = response.usageTracking || {};
      }

      // Load usage stats
      const usageResponse = await chrome.runtime.sendMessage({ action: 'getUsageStats' });
      if (usageResponse) {
        this.usageTracking = usageResponse.usage || {};
        this.dailyLimits = usageResponse.limits || {};
      }

      // Load settings using storage manager
      this.settings = await storageManager.getSettings();
      console.log('Options page loaded settings:', this.settings);
      console.log('Options page loaded:', this.blockedSites.length, 'blocked sites');

      // Phase 4: Load content filtering data
      await this.loadContentFilteringData();

      // Phase 5: Load schedule and redirect data
      await this.loadScheduleData();
      await this.loadSmartRedirectData();
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }

  setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const sectionId = link.getAttribute('data-section');
        this.showSection(sectionId);

        // Update active nav link
        navLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
      });
    });
  }

  showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));

    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
      targetSection.classList.add('active');
    }
  }

  setupEventListeners() {
    // Main protection toggle
    const mainToggle = document.getElementById('mainProtectionToggle');
    if (mainToggle) {
      mainToggle.addEventListener('click', () => this.toggleMainProtection());
    }

    // Add site button
    const addSiteBtn = document.getElementById('addSiteBtn');
    if (addSiteBtn) {
      addSiteBtn.addEventListener('click', () => this.addSite());
    }

    // New site input enter key
    const newSiteInput = document.getElementById('newSiteInput');
    if (newSiteInput) {
      newSiteInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.addSite();
        }
      });
    }

    // Settings toggles
    this.setupToggle('incognitoToggle', 'workInIncognito');
    this.setupToggle('notificationsToggle', 'showNotifications');
    this.setupToggle('analyticsToggle', 'enableTimeTracking');
    this.setupToggle('timeLimitsToggle', 'enableTimeLimits');
    this.setupToggle('autoStartBreaksToggle', 'focusMode.autoStartBreaks');
    this.setupToggle('passwordProtectionToggle', 'passwordProtection');

    // Phase 4: Content filtering toggles
    this.setupContentFilteringToggle('phraseBlockingToggle', 'enablePhraseBlocking');
    this.setupContentFilteringToggle('adultContentToggle', 'enableAdultContentBlocking');
    this.setupContentFilteringToggle('safeSearchToggle', 'forceSafeSearch');

    // Settings dropdowns
    const defaultBlockDuration = document.getElementById('defaultBlockDuration');
    if (defaultBlockDuration) {
      defaultBlockDuration.addEventListener('change', () => {
        this.updateSetting('defaultBlockDuration', defaultBlockDuration.value);
      });
    }

    const defaultDailyLimit = document.getElementById('defaultDailyLimit');
    if (defaultDailyLimit) {
      defaultDailyLimit.addEventListener('change', () => {
        this.updateSetting('defaultDailyLimit', parseInt(defaultDailyLimit.value.replace(' minutes', '').replace(' hours', '') * (defaultDailyLimit.value.includes('hours') ? 60 : 1)));
      });
    }

    const workSessionDuration = document.getElementById('workSessionDuration');
    if (workSessionDuration) {
      workSessionDuration.addEventListener('change', () => {
        this.updateSetting('focusMode.workDuration', parseInt(workSessionDuration.value.replace(' minutes', '')));
      });
    }

    const breakDuration = document.getElementById('breakDuration');
    if (breakDuration) {
      breakDuration.addEventListener('change', () => {
        this.updateSetting('focusMode.breakDuration', parseInt(breakDuration.value.replace(' minutes', '')));
      });
    }

    // Action buttons
    const exportDataBtn = document.getElementById('exportDataBtn');
    if (exportDataBtn) {
      exportDataBtn.addEventListener('click', () => this.exportData());
    }

    const importDataBtn = document.getElementById('importDataBtn');
    if (importDataBtn) {
      importDataBtn.addEventListener('click', () => this.importData());
    }

    const resetSettingsBtn = document.getElementById('resetSettingsBtn');
    if (resetSettingsBtn) {
      resetSettingsBtn.addEventListener('click', () => this.resetSettings());
    }

    const clearDataBtn = document.getElementById('clearDataBtn');
    if (clearDataBtn) {
      clearDataBtn.addEventListener('click', () => this.clearAllData());
    }

    const updatePasswordBtn = document.getElementById('updatePasswordBtn');
    if (updatePasswordBtn) {
      updatePasswordBtn.addEventListener('click', () => this.updateMasterPassword());
    }

    // File input for import
    const importFileInput = document.getElementById('importFileInput');
    if (importFileInput) {
      importFileInput.addEventListener('change', (e) => this.handleFileImport(e));
    }

    // Phase 4: Phrase management (new tag-based interface)
    this.setupPhraseManagementEvents();

    // Phase 5: Schedule management
    this.setupScheduleEventListeners();

    // Phase 5: Smart redirect management
    this.setupSmartRedirectEventListeners();
  }

  setupToggle(toggleId, settingKey) {
    const toggle = document.getElementById(toggleId);
    if (toggle) {
      toggle.addEventListener('click', () => {
        const isActive = toggle.classList.contains('active');
        this.updateSetting(settingKey, !isActive);

        if (isActive) {
          toggle.classList.remove('active');
        } else {
          toggle.classList.add('active');
        }
      });
    }
  }

  updateUI() {
    this.updateStats();
    this.updateMainToggle();
    this.updateBlockedSitesList();
    this.updateSettingsUI();
    // Phase 4: Update content filtering UI
    this.updateContentFilteringUI();
    // Phase 5: Update schedule and redirect UI
    this.updateScheduleUI();
    this.updateSmartRedirectUI();
  }

  updateStats() {
    // Update stats in overview section
    const blockedSitesCount = document.getElementById('blockedSitesCount');
    if (blockedSitesCount) {
      blockedSitesCount.textContent = this.blockedSites.length;
    }

    // Calculate real stats from usage data
    const blocksToday = document.getElementById('blocksToday');
    if (blocksToday) {
      // Count how many blocked sites have usage data (indicating block attempts)
      let blockCount = 0;
      if (this.usageTracking) {
        for (const domain of this.blockedSites) {
          if (this.usageTracking[domain]) {
            blockCount++;
          }
        }
      }
      blocksToday.textContent = blockCount;
    }

    const timeSaved = document.getElementById('timeSaved');
    if (timeSaved) {
      // Calculate total time that would have been spent on blocked sites
      let totalMinutes = 0;
      if (this.usageTracking) {
        for (const domain of this.blockedSites) {
          if (this.usageTracking[domain]) {
            totalMinutes += this.usageTracking[domain];
          }
        }
      }
      const hours = Math.floor(totalMinutes / 60);
      timeSaved.textContent = hours > 0 ? `${hours}h` : `${totalMinutes}m`;
    }

    const successRate = document.getElementById('successRate');
    if (successRate) {
      // Calculate success rate based on blocked vs total sites
      const totalSites = this.blockedSites.length;
      const activelySites = Object.keys(this.usageTracking || {}).length;
      const rate = totalSites > 0 ? Math.round((totalSites / Math.max(totalSites, activelySites)) * 100) : 100;
      successRate.textContent = `${Math.min(rate, 100)}%`;
    }
  }

  updateMainToggle() {
    const mainToggle = document.getElementById('mainProtectionToggle');
    if (mainToggle) {
      if (this.isEnabled) {
        mainToggle.classList.add('active');
      } else {
        mainToggle.classList.remove('active');
      }
    }
  }

  updateBlockedSitesList() {
    const sitesList = document.getElementById('blockedSitesList');
    const emptyState = document.getElementById('emptyBlockedSites');

    if (!sitesList) return;

    if (this.blockedSites.length === 0) {
      sitesList.innerHTML = '';
      sitesList.appendChild(emptyState);
      return;
    }

    sitesList.innerHTML = '';

    this.blockedSites.forEach(domain => {
      const siteItem = this.createSiteItem(domain);
      sitesList.appendChild(siteItem);
    });
  }

  createSiteItem(domain) {
    const item = document.createElement('div');
    item.className = 'site-item';

    const category = this.getSiteCategory(domain);
    const blockStatus = this.getBlockStatus(domain);

    item.innerHTML = `
      <div class="site-info">
        <div class="site-icon" style="background: ${this.getSiteColor(domain)};">
          ${this.getSiteIcon(domain)}
        </div>
        <div class="site-details">
          <h5>${domain}</h5>
          <p>${category}</p>
        </div>
      </div>
      <div class="time-badge">${blockStatus}</div>
    `;

    // Add remove functionality on click
    item.addEventListener('click', (e) => {
      if (e.target.closest('.time-badge')) {
        this.removeSite(domain);
      }
    });

    return item;
  }

  updateSettingsUI() {
    console.log('Updating settings UI with:', this.settings);

    // Update toggle states
    const toggles = {
      'incognitoToggle': this.settings.workInIncognito,
      'notificationsToggle': this.settings.showNotifications,
      'analyticsToggle': this.settings.enableTimeTracking,
      'timeLimitsToggle': this.settings.enableTimeLimits,
      'autoStartBreaksToggle': this.settings.focusMode?.autoStartBreaks,
      'passwordProtectionToggle': this.settings.passwordProtection
    };

    Object.entries(toggles).forEach(([toggleId, value]) => {
      const toggle = document.getElementById(toggleId);
      if (toggle) {
        console.log('Setting toggle', toggleId, 'to', value);
        if (value) {
          toggle.classList.add('active');
        } else {
          toggle.classList.remove('active');
        }
      }
    });

    // Update dropdown values
    const defaultBlockDuration = document.getElementById('defaultBlockDuration');
    if (defaultBlockDuration) {
      defaultBlockDuration.value = this.settings.defaultBlockDuration || '5days';
    }

    const defaultDailyLimit = document.getElementById('defaultDailyLimit');
    if (defaultDailyLimit) {
      const limitMinutes = this.settings.defaultDailyLimit || 120;
      if (limitMinutes >= 60) {
        defaultDailyLimit.value = `${Math.floor(limitMinutes / 60)} hours`;
      } else {
        defaultDailyLimit.value = `${limitMinutes} minutes`;
      }
    }

    const workSessionDuration = document.getElementById('workSessionDuration');
    if (workSessionDuration) {
      workSessionDuration.value = `${this.settings.focusMode?.workDuration || 25} minutes`;
    }

    const breakDuration = document.getElementById('breakDuration');
    if (breakDuration) {
      breakDuration.value = `${this.settings.focusMode?.breakDuration || 5} minutes`;
    }
  }

  async toggleMainProtection() {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({ action: 'toggleBlocking' });
      if (response && response.success) {
        this.isEnabled = response.isEnabled;
        this.updateMainToggle();
        this.showNotification(
          this.isEnabled ? 'Protection enabled' : 'Protection disabled',
          'success'
        );
      }
    } catch (error) {
      console.error('Error toggling protection:', error);
      this.showNotification('Failed to toggle protection', 'error');
    }

    this.showLoading(false);
  }

  async addSite() {
    const input = document.getElementById('newSiteInput');
    const url = input.value.trim();

    if (!url) return;

    const domain = this.extractDomain(url) || url;

    if (this.blockedSites.includes(domain)) {
      this.showNotification('Site is already blocked', 'warning');
      return;
    }

    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'addBlockedSite',
        url: domain
      });

      if (response && response.success) {
        this.blockedSites.push(domain);
        input.value = '';
        this.updateUI();
        this.showNotification(`${domain} has been blocked`, 'success');
      }
    } catch (error) {
      console.error('Error adding site:', error);
      this.showNotification('Failed to block site', 'error');
    }

    this.showLoading(false);
  }

  async removeSite(domain) {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'removeBlockedSite',
        url: domain
      });

      if (response && response.success) {
        this.blockedSites = this.blockedSites.filter(site => site !== domain);
        this.updateUI();
        this.showNotification(`${domain} has been unblocked`, 'success');
      }
    } catch (error) {
      console.error('Error removing site:', error);
      this.showNotification('Failed to unblock site', 'error');
    }

    this.showLoading(false);
  }

  async updateSetting(key, value) {
    console.log('Updating setting:', key, '=', value);

    // Handle nested settings like 'focusMode.autoStartBreaks'
    if (key.includes('.')) {
      const [parentKey, childKey] = key.split('.');
      if (!this.settings[parentKey]) {
        this.settings[parentKey] = {};
      }
      this.settings[parentKey][childKey] = value;
    } else {
      this.settings[key] = value;
    }

    try {
      await storageManager.setSettings(this.settings);
      console.log('Setting saved successfully:', this.settings);
      this.showNotification('Setting updated', 'success');
    } catch (error) {
      console.error('Error updating setting:', error);
      this.showNotification('Failed to update setting', 'error');
    }
  }

  async exportData() {
    try {
      const data = await storageManager.exportData();
      if (data) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `focusguard-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        this.showNotification('Data exported successfully', 'success');
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      this.showNotification('Failed to export data', 'error');
    }
  }

  importData() {
    const fileInput = document.getElementById('importFileInput');
    if (fileInput) {
      fileInput.click();
    }
  }

  async handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const text = await file.text();
      const data = JSON.parse(text);

      if (await storageManager.importData(data)) {
        this.showNotification('Data imported successfully', 'success');
        // Reload the page to reflect changes
        setTimeout(() => window.location.reload(), 1500);
      } else {
        this.showNotification('Failed to import data', 'error');
      }
    } catch (error) {
      console.error('Error importing data:', error);
      this.showNotification('Invalid file format', 'error');
    }
  }

  async resetSettings() {
    if (!confirm('Are you sure you want to reset all settings to default values?')) {
      return;
    }

    try {
      this.settings = storageManager.getDefaultSettings();
      await storageManager.setSettings(this.settings);
      this.updateSettingsUI();
      this.showNotification('Settings reset to defaults', 'success');
    } catch (error) {
      console.error('Error resetting settings:', error);
      this.showNotification('Failed to reset settings', 'error');
    }
  }

  async clearAllData() {
    if (!confirm('Are you sure you want to permanently delete all data? This action cannot be undone.')) {
      return;
    }

    try {
      await storageManager.clear();
      this.showNotification('All data cleared', 'success');
      // Reload the page
      setTimeout(() => window.location.reload(), 1500);
    } catch (error) {
      console.error('Error clearing data:', error);
      this.showNotification('Failed to clear data', 'error');
    }
  }

  async updateMasterPassword() {
    const passwordInput = document.getElementById('masterPassword');
    const password = passwordInput.value.trim();

    if (!password) {
      this.showNotification('Please enter a password', 'warning');
      return;
    }

    if (password.length < 6) {
      this.showNotification('Password must be at least 6 characters', 'warning');
      return;
    }

    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'setMasterPassword',
        password: password
      });

      if (response && response.success) {
        passwordInput.value = '';
        this.showNotification('Master password updated successfully', 'success');
      } else {
        this.showNotification('Failed to update password', 'error');
      }
    } catch (error) {
      console.error('Error updating password:', error);
      this.showNotification('Failed to update password', 'error');
    }

    this.showLoading(false);
  }

  // Phase 4: Content Filtering Methods
  async loadContentFilteringData() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getContentFilteringStatus'
      });

      if (response) {
        this.contentFiltering = response.contentFiltering || {};
        console.log('Content filtering settings loaded:', this.contentFiltering);
      }

      const phrasesResponse = await chrome.runtime.sendMessage({
        action: 'getBlockedPhrases'
      });

      if (phrasesResponse) {
        this.blockedPhrases = phrasesResponse.phrases || [];
        console.log('Blocked phrases loaded:', this.blockedPhrases.length);
      }

      // Load protection disabling status
      const disableStatusResponse = await chrome.runtime.sendMessage({
        action: 'getProtectionDisableStatus'
      });

      if (disableStatusResponse) {
        this.protectionDisabling = disableStatusResponse.protectionDisabling || {};
        console.log('Protection disabling status loaded:', this.protectionDisabling);
      }

      this.updateContentFilteringUI();
    } catch (error) {
      console.error('Error loading content filtering data:', error);
    }
  }

  setupContentFilteringToggle(toggleId, settingKey) {
    const toggle = document.getElementById(toggleId);
    if (toggle) {
      toggle.addEventListener('click', async () => {
        const isActive = toggle.classList.contains('active');

        // Special handling for adult content blocking disable
        if (settingKey === 'enableAdultContentBlocking' && isActive) {
          await this.handleAdultContentDisableRequest(toggle);
          return;
        }

        const newValue = !isActive;

        // Update local state
        this.contentFiltering[settingKey] = newValue;

        // Update UI immediately
        if (newValue) {
          toggle.classList.add('active');
        } else {
          toggle.classList.remove('active');
        }

        // Save to background
        try {
          const response = await chrome.runtime.sendMessage({
            action: 'updateContentFiltering',
            settings: { [settingKey]: newValue }
          });

          if (response && response.success) {
            this.showNotification('Content filtering setting updated', 'success');
          } else {
            // Revert UI on failure
            if (newValue) {
              toggle.classList.remove('active');
            } else {
              toggle.classList.add('active');
            }
            this.showNotification('Failed to update setting', 'error');
          }
        } catch (error) {
          console.error('Error updating content filtering setting:', error);
          this.showNotification('Failed to update setting', 'error');
        }
      });
    }
  }

  // Phase 4: Handle adult content protection disable request with countdown
  async handleAdultContentDisableRequest(toggle) {
    // Check if already counting down
    if (this.protectionDisabling.isCountingDown) {
      const result = confirm('A countdown is already in progress. Do you want to cancel it?');
      if (result) {
        await this.cancelCountdown();
      }
      return;
    }

    // Show warning and start countdown
    const result = confirm(
      'WARNING: You are about to disable adult content protection.\n\n' +
      'This will start a 1-hour countdown. After the countdown completes, ' +
      'protection will be disabled for only 10 minutes before automatically re-enabling.\n\n' +
      'Are you sure you want to proceed?'
    );

    if (result) {
      await this.startProtectionDisableCountdown();
    }
  }

  // Phase 4: Start protection disable countdown
  async startProtectionDisableCountdown() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'startProtectionDisableCountdown'
      });

      if (response && response.success) {
        this.protectionDisabling.isCountingDown = true;
        this.protectionDisabling.countdownStartTime = response.countdownStartTime;
        this.updateCountdownUI();
        this.startCountdownTimer();
        this.showNotification('Protection disable countdown started (1 hour)', 'warning');
      } else {
        this.showNotification('Failed to start countdown', 'error');
      }
    } catch (error) {
      console.error('Error starting countdown:', error);
      this.showNotification('Failed to start countdown', 'error');
    }
  }

  // Phase 4: Cancel protection disable countdown
  async cancelCountdown() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'cancelProtectionDisableCountdown'
      });

      if (response && response.success) {
        this.protectionDisabling.isCountingDown = false;
        this.protectionDisabling.countdownStartTime = null;
        this.stopCountdownTimer();
        this.updateCountdownUI();
        this.showNotification('Countdown cancelled', 'success');
      }
    } catch (error) {
      console.error('Error cancelling countdown:', error);
    }
  }

  // Phase 4: Start countdown timer UI
  startCountdownTimer() {
    this.stopCountdownTimer(); // Clear any existing timer

    this.countdownTimer = setInterval(() => {
      this.updateCountdownUI();
    }, 1000); // Update every second
  }

  // Phase 4: Stop countdown timer
  stopCountdownTimer() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  }

  updateContentFilteringUI() {
    // Update content filtering toggles
    const toggles = {
      'phraseBlockingToggle': this.contentFiltering.enablePhraseBlocking,
      'adultContentToggle': this.contentFiltering.enableAdultContentBlocking,
      'safeSearchToggle': this.contentFiltering.forceSafeSearch
    };

    Object.entries(toggles).forEach(([toggleId, value]) => {
      const toggle = document.getElementById(toggleId);
      if (toggle) {
        if (value) {
          toggle.classList.add('active');
        } else {
          toggle.classList.remove('active');
        }
      }
    });

    // Update phrases display with new tag-based interface
    this.updatePhrasesDisplay();

    // Update notes to show phrase-based system is implemented
    const phraseNote = document.querySelector('#phrases .section-content p');
    if (phraseNote) {
      phraseNote.innerHTML = `
        <strong>Active:</strong> Phrase blocking is now active.
        ${this.blockedPhrases.length} user-defined phrases are being monitored. Only multi-word phrases are allowed to prevent false positives.
      `;
      phraseNote.style.color = '#48bb78';
    }

    const adultNote = document.querySelector('#adult .section-content p');
    if (adultNote) {
      adultNote.innerHTML = `
        <strong>Active:</strong> Adult content protection is now active.
        Monitoring ${this.contentFiltering.adultSitesCount || 0}+ adult websites and content patterns.
      `;
      adultNote.style.color = '#48bb78';
    }

    // Update countdown UI
    this.updateCountdownUI();
  }

  // Phase 4: Update countdown UI
  updateCountdownUI() {
    const adultToggle = document.getElementById('adultContentToggle');
    if (!adultToggle) return;

    // Remove existing countdown UI
    const existingCountdown = document.querySelector('.countdown-container');
    if (existingCountdown) {
      existingCountdown.remove();
    }

    if (this.protectionDisabling.isCountingDown && this.protectionDisabling.countdownStartTime) {
      const now = Date.now();
      const elapsed = now - this.protectionDisabling.countdownStartTime;
      const remaining = Math.max(0, (60 * 60 * 1000) - elapsed); // 1 hour countdown

      if (remaining > 0) {
        // Create countdown UI
        const countdownContainer = document.createElement('div');
        countdownContainer.className = 'countdown-container';
        countdownContainer.style.cssText = `
          margin-top: 12px;
          padding: 16px;
          background: #fef5e7;
          border: 2px solid #f6e05e;
          border-radius: 8px;
          text-align: center;
        `;

        const hours = Math.floor(remaining / (1000 * 60 * 60));
        const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((remaining % (1000 * 60)) / 1000);

        countdownContainer.innerHTML = `
          <div style="color: #744210; font-weight: 600; margin-bottom: 8px;">
            ⏰ Protection Disable Countdown
          </div>
          <div style="font-size: 24px; font-weight: bold; color: #c53030; margin-bottom: 8px;">
            ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}
          </div>
          <div style="font-size: 12px; color: #744210; margin-bottom: 12px;">
            After countdown: 10-minute temporary disable, then auto re-enable
          </div>
          <button id="cancelCountdownBtn" style="
            background: #c53030;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 8px;
          ">Cancel Countdown</button>
          ${remaining < 1000 ? `
            <button id="confirmDisableBtn" style="
              background: #d69e2e;
              color: white;
              border: none;
              padding: 8px 16px;
              border-radius: 6px;
              font-size: 12px;
              cursor: pointer;
            ">Confirm Disable (10 min)</button>
          ` : ''}
        `;

        // Insert after the adult content toggle
        adultToggle.parentNode.insertBefore(countdownContainer, adultToggle.nextSibling);

        // Add event listeners
        const cancelBtn = document.getElementById('cancelCountdownBtn');
        if (cancelBtn) {
          cancelBtn.addEventListener('click', () => this.cancelCountdown());
        }

        const confirmBtn = document.getElementById('confirmDisableBtn');
        if (confirmBtn) {
          confirmBtn.addEventListener('click', () => this.confirmProtectionDisable());
        }
      } else {
        // Countdown completed, show confirm button
        this.protectionDisabling.isCountingDown = false;
        this.stopCountdownTimer();
      }
    }
  }

  // Phase 4: Confirm protection disable after countdown
  async confirmProtectionDisable() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'confirmProtectionDisable'
      });

      if (response && response.success) {
        this.showNotification('Protection disabled for 10 minutes', 'warning');
        this.updateCountdownUI();

        // Show temporary disable status
        setTimeout(() => {
          this.loadContentFilteringData(); // Refresh status
        }, 1000);
      } else {
        this.showNotification(response.error || 'Failed to disable protection', 'error');
      }
    } catch (error) {
      console.error('Error confirming protection disable:', error);
      this.showNotification('Failed to disable protection', 'error');
    }
  }

  // New phrase management methods
  setupPhraseManagementEvents() {
    // Add phrase button
    const addPhraseBtn = document.getElementById('addPhraseBtn');
    if (addPhraseBtn) {
      addPhraseBtn.addEventListener('click', () => this.addPhrase());
    }

    // Add phrase input enter key
    const newPhraseInput = document.getElementById('newPhraseInput');
    if (newPhraseInput) {
      newPhraseInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.addPhrase();
        }
      });
    }
  }

  async addPhrase() {
    const input = document.getElementById('newPhraseInput');
    if (!input) return;

    const phrase = input.value.trim();
    if (!phrase) {
      this.showNotification('Please enter a phrase', 'warning');
      return;
    }

    if (phrase.length > 100) {
      this.showNotification('Phrase too long (max 100 characters)', 'warning');
      return;
    }

    // Validate that it's a multi-word phrase
    if (phrase.split(' ').length < 2) {
      this.showNotification('Phrase must contain at least 2 words to prevent false positives', 'warning');
      return;
    }

    // Check if phrase already exists
    if (this.blockedPhrases.includes(phrase.toLowerCase())) {
      this.showNotification('Phrase already exists', 'warning');
      return;
    }

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'addCustomPhrase',
        phrase: phrase
      });

      if (response && response.success) {
        this.blockedPhrases.push(phrase.toLowerCase());
        input.value = '';
        this.updatePhrasesDisplay();
        this.showNotification('Phrase added successfully', 'success');
      } else {
        this.showNotification(response.error || 'Failed to add phrase', 'error');
      }
    } catch (error) {
      console.error('Error adding phrase:', error);
      this.showNotification('Failed to add phrase', 'error');
    }
  }

  async removePhrase(phrase) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'removeCustomPhrase',
        phrase: phrase
      });

      if (response && response.success) {
        this.blockedPhrases = this.blockedPhrases.filter(p => p !== phrase);
        this.updatePhrasesDisplay();
        this.showNotification('Phrase removed successfully', 'success');
      } else {
        this.showNotification('Failed to remove phrase', 'error');
      }
    } catch (error) {
      console.error('Error removing phrase:', error);
      this.showNotification('Failed to remove phrase', 'error');
    }
  }

  // Removed keyword category methods - now using user-defined phrases only

  updatePhrasesDisplay() {
    const container = document.getElementById('phrasesContainer');
    const countElement = document.getElementById('phraseCount');

    if (!container || !countElement) return;

    // Clear container
    container.innerHTML = '';

    if (this.blockedPhrases.length === 0) {
      container.classList.add('empty');
      container.textContent = 'No phrases blocked yet. Add multi-word phrases above to prevent false positives.';
    } else {
      container.classList.remove('empty');

      this.blockedPhrases.forEach(phrase => {
        const tag = document.createElement('div');
        tag.className = 'phrase-tag user-defined';
        tag.innerHTML = `
          <span>${phrase}</span>
          <button class="phrase-remove" title="Remove phrase">×</button>
        `;

        // Add remove functionality
        const removeBtn = tag.querySelector('.phrase-remove');
        removeBtn.addEventListener('click', () => this.removePhrase(phrase));

        container.appendChild(tag);
      });
    }

    // Update count
    countElement.textContent = `${this.blockedPhrases.length} phrase${this.blockedPhrases.length !== 1 ? 's' : ''} blocked`;
  }

  // Phase 5: Schedule Management Methods
  async loadScheduleData() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getScheduleSettings'
      });

      if (response) {
        this.scheduleSettings = response.scheduleSettings || {};
        console.log('Schedule settings loaded:', this.scheduleSettings);
      }
    } catch (error) {
      console.error('Error loading schedule data:', error);
    }
  }

  async loadSmartRedirectData() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getSmartRedirects'
      });

      if (response) {
        this.smartRedirects = response.smartRedirects || {};
        console.log('Smart redirects loaded:', this.smartRedirects);
      }
    } catch (error) {
      console.error('Error loading smart redirect data:', error);
    }
  }

  setupScheduleEventListeners() {
    // Schedule toggle
    this.setupToggle('scheduleToggle', 'enableScheduleMode');

    // Schedule type change
    const scheduleType = document.getElementById('scheduleType');
    if (scheduleType) {
      scheduleType.addEventListener('change', () => {
        this.updateSetting('scheduleType', scheduleType.value);
        this.updateScheduleUI();
      });
    }

    // Schedule profile change
    const scheduleProfile = document.getElementById('scheduleProfile');
    if (scheduleProfile) {
      scheduleProfile.addEventListener('change', () => {
        this.applyScheduleProfile(scheduleProfile.value);
      });
    }

    // Day toggles and time inputs
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    days.forEach(day => {
      const toggle = document.getElementById(`${day}Toggle`);
      const startInput = document.getElementById(`${day}Start`);
      const endInput = document.getElementById(`${day}End`);

      if (toggle) {
        toggle.addEventListener('click', () => {
          const isActive = toggle.classList.contains('active');
          this.updateScheduleDay(day, 'enabled', !isActive);
          if (isActive) {
            toggle.classList.remove('active');
          } else {
            toggle.classList.add('active');
          }
        });
      }

      if (startInput) {
        startInput.addEventListener('change', () => {
          this.updateScheduleDay(day, 'start', startInput.value);
        });
      }

      if (endInput) {
        endInput.addEventListener('change', () => {
          this.updateScheduleDay(day, 'end', endInput.value);
        });
      }
    });

    // Apply to all days button
    const applyToAllBtn = document.getElementById('applyToAllDaysBtn');
    if (applyToAllBtn) {
      applyToAllBtn.addEventListener('click', () => this.applyToAllDays());
    }

    // Save schedule button
    const saveScheduleBtn = document.getElementById('saveScheduleBtn');
    if (saveScheduleBtn) {
      saveScheduleBtn.addEventListener('click', () => this.saveSchedule());
    }

    // Emergency override
    const emergencyOverrideBtn = document.getElementById('emergencyOverrideBtn');
    if (emergencyOverrideBtn) {
      emergencyOverrideBtn.addEventListener('click', () => this.startEmergencyOverride());
    }

    // Cancel override
    const cancelOverrideBtn = document.getElementById('cancelOverrideBtn');
    if (cancelOverrideBtn) {
      cancelOverrideBtn.addEventListener('click', () => this.cancelEmergencyOverride());
    }
  }

  setupSmartRedirectEventListeners() {
    // Smart redirect toggle
    this.setupToggle('smartRedirectToggle', 'enableSmartRedirects');

    // Redirect category selects
    const redirectSelects = document.querySelectorAll('.redirect-select');
    redirectSelects.forEach(select => {
      select.addEventListener('change', (e) => {
        this.updateCategoryRedirect(e.target);
      });
    });

    // Custom redirect add button
    const customRedirectItem = document.querySelector('.custom-redirect-item');
    if (customRedirectItem) {
      const addBtn = customRedirectItem.querySelector('.btn');
      if (addBtn) {
        addBtn.addEventListener('click', () => this.addCustomRedirect());
      }
    }
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
      return null;
    }
  }

  getSiteIcon(domain) {
    const icons = {
      'facebook.com': 'f',
      'twitter.com': '𝕏',
      'youtube.com': '▶',
      'instagram.com': '📷',
      'reddit.com': '🤖',
      'tiktok.com': '🎵'
    };
    return icons[domain] || '🌐';
  }

  getSiteColor(domain) {
    const colors = {
      'facebook.com': '#1877F2',
      'twitter.com': '#1DA1F2',
      'youtube.com': '#FF0000',
      'instagram.com': '#E4405F',
      'reddit.com': '#FF4500',
      'tiktok.com': '#000000'
    };
    return colors[domain] || '#667eea';
  }

  getSiteCategory(domain) {
    const categories = {
      'facebook.com': 'Social Media',
      'twitter.com': 'Social Media',
      'instagram.com': 'Social Media',
      'tiktok.com': 'Social Media',
      'youtube.com': 'Entertainment',
      'reddit.com': 'Social Media',
      'netflix.com': 'Entertainment',
      'twitch.tv': 'Entertainment',
      'amazon.com': 'Shopping',
      'ebay.com': 'Shopping'
    };
    return categories[domain] || 'Website';
  }

  getBlockStatus(domain) {
    // Check if temporarily unblocked
    if (this.temporaryUnblocks && this.temporaryUnblocks[domain]) {
      const endTime = this.temporaryUnblocks[domain];
      const now = Date.now();
      if (now < endTime) {
        const remainingMinutes = Math.ceil((endTime - now) / (1000 * 60));
        if (remainingMinutes > 60) {
          const hours = Math.ceil(remainingMinutes / 60);
          return `${hours}h left`;
        } else {
          return `${remainingMinutes}m left`;
        }
      }
    }

    // Check if has daily limit
    if (this.settings.enableTimeLimits && this.dailyLimits && this.dailyLimits[domain]) {
      return `${this.dailyLimits[domain]}min daily limit`;
    }

    // Default status
    return 'Permanently blocked';
  }

  showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      if (show) {
        overlay.classList.add('show');
      } else {
        overlay.classList.remove('show');
      }
    }
  }

  showNotification(message, type = 'info') {
    // Create a simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Add styles
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      animation: slideIn 0.3s ease;
      max-width: 300px;
      word-wrap: break-word;
    `;

    // Set background color based on type
    switch (type) {
      case 'success':
        notification.style.background = '#48bb78';
        break;
      case 'error':
        notification.style.background = '#f56565';
        break;
      case 'warning':
        notification.style.background = '#ed8936';
        break;
      case 'info':
      default:
        notification.style.background = '#667eea';
        break;
    }

    // Add animation styles if not already added
    if (!document.getElementById('notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  // Phase 5: Schedule Management Implementation
  updateScheduleDay(day, property, value) {
    if (!this.scheduleSettings.dailySchedule) {
      this.scheduleSettings.dailySchedule = {};
    }
    if (!this.scheduleSettings.dailySchedule[day]) {
      this.scheduleSettings.dailySchedule[day] = {
        enabled: true,
        start: '09:00',
        end: '17:00'
      };
    }
    this.scheduleSettings.dailySchedule[day][property] = value;
  }

  applyScheduleProfile(profile) {
    const profiles = {
      work: { start: '09:00', end: '17:00', weekdays: true, weekends: false },
      study: { start: '08:00', end: '18:00', weekdays: true, weekends: true },
      evening: { start: '18:00', end: '22:00', weekdays: true, weekends: true }
    };

    if (profiles[profile]) {
      const config = profiles[profile];
      const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

      days.forEach((day, index) => {
        const isWeekend = index >= 5;
        const enabled = isWeekend ? config.weekends : config.weekdays;

        this.updateScheduleDay(day, 'enabled', enabled);
        this.updateScheduleDay(day, 'start', config.start);
        this.updateScheduleDay(day, 'end', config.end);

        // Update UI
        const toggle = document.getElementById(`${day}Toggle`);
        const startInput = document.getElementById(`${day}Start`);
        const endInput = document.getElementById(`${day}End`);

        if (toggle) {
          if (enabled) {
            toggle.classList.add('active');
          } else {
            toggle.classList.remove('active');
          }
        }

        if (startInput) startInput.value = config.start;
        if (endInput) endInput.value = config.end;
      });

      this.showNotification(`Applied ${profile} schedule profile`, 'success');
    }
  }

  applyToAllDays() {
    const mondayStart = document.getElementById('mondayStart').value;
    const mondayEnd = document.getElementById('mondayEnd').value;
    const mondayEnabled = document.getElementById('mondayToggle').classList.contains('active');

    const days = ['tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    days.forEach(day => {
      this.updateScheduleDay(day, 'start', mondayStart);
      this.updateScheduleDay(day, 'end', mondayEnd);
      this.updateScheduleDay(day, 'enabled', mondayEnabled);

      // Update UI
      const startInput = document.getElementById(`${day}Start`);
      const endInput = document.getElementById(`${day}End`);
      const toggle = document.getElementById(`${day}Toggle`);

      if (startInput) startInput.value = mondayStart;
      if (endInput) endInput.value = mondayEnd;
      if (toggle) {
        if (mondayEnabled) {
          toggle.classList.add('active');
        } else {
          toggle.classList.remove('active');
        }
      }
    });

    this.showNotification('Applied Monday schedule to all days', 'success');
  }

  async saveSchedule() {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'updateScheduleSettings',
        scheduleSettings: this.scheduleSettings
      });

      if (response && response.success) {
        this.showNotification('Schedule saved successfully', 'success');
      } else {
        this.showNotification('Failed to save schedule', 'error');
      }
    } catch (error) {
      console.error('Error saving schedule:', error);
      this.showNotification('Failed to save schedule', 'error');
    }

    this.showLoading(false);
  }

  async startEmergencyOverride() {
    const duration = parseInt(document.getElementById('overrideDuration').value);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'startScheduleOverride',
        durationMinutes: duration
      });

      if (response && response.success) {
        this.showNotification(`Schedule override active for ${duration} minutes`, 'warning');
        this.startOverrideTimer(duration);
        this.updateOverrideUI(true, duration);
      } else {
        this.showNotification('Failed to start override', 'error');
      }
    } catch (error) {
      console.error('Error starting override:', error);
      this.showNotification('Failed to start override', 'error');
    }
  }

  async cancelEmergencyOverride() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'cancelScheduleOverride'
      });

      if (response && response.success) {
        this.showNotification('Schedule override cancelled', 'success');
        this.stopOverrideTimer();
        this.updateOverrideUI(false);
      }
    } catch (error) {
      console.error('Error cancelling override:', error);
    }
  }

  startOverrideTimer(durationMinutes) {
    this.stopOverrideTimer();

    let remainingSeconds = durationMinutes * 60;

    this.overrideTimer = setInterval(() => {
      remainingSeconds--;

      if (remainingSeconds <= 0) {
        this.stopOverrideTimer();
        this.updateOverrideUI(false);
        this.showNotification('Schedule override expired', 'info');
        return;
      }

      const minutes = Math.floor(remainingSeconds / 60);
      const seconds = remainingSeconds % 60;
      const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      const timeElement = document.getElementById('overrideTimeRemaining');
      if (timeElement) {
        timeElement.textContent = timeString;
      }
    }, 1000);
  }

  stopOverrideTimer() {
    if (this.overrideTimer) {
      clearInterval(this.overrideTimer);
      this.overrideTimer = null;
    }
  }

  updateOverrideUI(isActive, durationMinutes = 0) {
    const overrideStatus = document.getElementById('overrideStatus');
    const emergencyBtn = document.getElementById('emergencyOverrideBtn');

    if (overrideStatus) {
      if (isActive) {
        overrideStatus.style.display = 'block';
        const timeString = `${durationMinutes.toString().padStart(2, '0')}:00`;
        const timeElement = document.getElementById('overrideTimeRemaining');
        if (timeElement) {
          timeElement.textContent = timeString;
        }
      } else {
        overrideStatus.style.display = 'none';
      }
    }

    if (emergencyBtn) {
      emergencyBtn.disabled = isActive;
      emergencyBtn.textContent = isActive ? 'Override Active' : 'Override (30 min)';
    }
  }

  updateScheduleUI() {
    // Update schedule toggle
    const scheduleToggle = document.getElementById('scheduleToggle');
    if (scheduleToggle && this.settings.enableScheduleMode) {
      scheduleToggle.classList.add('active');
    }

    // Update schedule type
    const scheduleType = document.getElementById('scheduleType');
    if (scheduleType && this.settings.scheduleType) {
      scheduleType.value = this.settings.scheduleType;
    }

    // Update daily schedule if available
    if (this.scheduleSettings.dailySchedule) {
      const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

      days.forEach(day => {
        const dayData = this.scheduleSettings.dailySchedule[day];
        if (dayData) {
          const toggle = document.getElementById(`${day}Toggle`);
          const startInput = document.getElementById(`${day}Start`);
          const endInput = document.getElementById(`${day}End`);

          if (toggle) {
            if (dayData.enabled) {
              toggle.classList.add('active');
            } else {
              toggle.classList.remove('active');
            }
          }

          if (startInput) startInput.value = dayData.start || '09:00';
          if (endInput) endInput.value = dayData.end || '17:00';
        }
      });
    }
  }

  // Phase 5: Smart Redirect Implementation
  updateCategoryRedirect(selectElement) {
    const category = selectElement.closest('.category-item').querySelector('h5').textContent;
    const redirectUrl = selectElement.value;

    if (redirectUrl === 'custom') {
      const customUrl = prompt('Enter custom redirect URL:');
      if (customUrl) {
        this.updateSmartRedirectSetting(category, customUrl);
      }
    } else {
      this.updateSmartRedirectSetting(category, redirectUrl);
    }
  }

  async updateSmartRedirectSetting(category, redirectUrl) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'updateSmartRedirect',
        category: category,
        redirectUrl: redirectUrl
      });

      if (response && response.success) {
        this.showNotification(`Updated ${category} redirect`, 'success');
      }
    } catch (error) {
      console.error('Error updating smart redirect:', error);
      this.showNotification('Failed to update redirect', 'error');
    }
  }

  addCustomRedirect() {
    const sourceInput = document.querySelector('.custom-source');
    const targetInput = document.querySelector('.custom-target');

    const source = sourceInput.value.trim();
    const target = targetInput.value.trim();

    if (!source || !target) {
      this.showNotification('Please enter both source and target URLs', 'warning');
      return;
    }

    this.saveCustomRedirect(source, target);
    sourceInput.value = '';
    targetInput.value = '';
  }

  async saveCustomRedirect(source, target) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'addCustomRedirect',
        source: source,
        target: target
      });

      if (response && response.success) {
        this.showNotification(`Added redirect: ${source} → ${target}`, 'success');
        this.updateCustomRedirectsList();
      }
    } catch (error) {
      console.error('Error saving custom redirect:', error);
      this.showNotification('Failed to save redirect', 'error');
    }
  }

  updateCustomRedirectsList() {
    const listContainer = document.getElementById('customRedirectsList');
    if (!listContainer || !this.smartRedirects.customRedirects) return;

    listContainer.innerHTML = '';

    Object.entries(this.smartRedirects.customRedirects).forEach(([source, target]) => {
      const entry = document.createElement('div');
      entry.className = 'custom-redirect-entry';
      entry.innerHTML = `
        <div class="redirect-info">
          <span class="redirect-source">${source}</span>
          <span class="arrow">→</span>
          <span class="redirect-target">${target}</span>
        </div>
        <button class="remove-redirect-btn" data-source="${source}">Remove</button>
      `;

      const removeBtn = entry.querySelector('.remove-redirect-btn');
      removeBtn.addEventListener('click', () => this.removeCustomRedirect(source));

      listContainer.appendChild(entry);
    });
  }

  async removeCustomRedirect(source) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'removeCustomRedirect',
        source: source
      });

      if (response && response.success) {
        this.showNotification(`Removed redirect for ${source}`, 'success');
        this.updateCustomRedirectsList();
      }
    } catch (error) {
      console.error('Error removing custom redirect:', error);
      this.showNotification('Failed to remove redirect', 'error');
    }
  }

  updateSmartRedirectUI() {
    // Update smart redirect toggle
    const smartRedirectToggle = document.getElementById('smartRedirectToggle');
    if (smartRedirectToggle && this.settings.enableSmartRedirects) {
      smartRedirectToggle.classList.add('active');
    }

    // Update custom redirects list
    this.updateCustomRedirectsList();
  }
}

// Initialize options page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new FocusGuardOptions();
});
