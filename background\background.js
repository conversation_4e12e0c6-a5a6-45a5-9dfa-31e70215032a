// FocusGuard Pro - Background Service Worker
// Phase 1: Basic site blocking functionality

class FocusGuardBackground {
  constructor() {
    this.blockedSites = new Set();
    this.isEnabled = true;
    this.temporaryUnblocks = new Map(); // domain -> unblock end time
    this.usageTracking = new Map(); // domain -> daily usage in minutes
    this.dailyLimits = new Map(); // domain -> limit in minutes
    this.focusMode = {
      isActive: false,
      isBreak: false,
      sessionStartTime: null,
      sessionDuration: 25, // minutes
      breakDuration: 5, // minutes
      sessionsCompleted: 0
    };
    this.timers = new Map(); // active timers

    // Phase 4: Content Filtering & Protection
    this.adultContentSites = new Set();
    this.blockedPhrases = new Set(); // Changed from blockedKeywords to blockedPhrases
    this.contentFiltering = {
      enableAdultContentBlocking: true,
      enablePhraseBlocking: true, // Changed from enableKeywordBlocking
      strictMode: false,
      customPhrases: [], // Changed from customKeywords
      forceSafeSearch: true
    };

    // Phase 4: Safety mechanisms for disabling protection
    this.protectionDisabling = {
      isCountingDown: false,
      countdownStartTime: null,
      countdownDuration: 60 * 60 * 1000, // 1 hour in milliseconds
      temporaryDisableEndTime: null,
      temporaryDisableDuration: 10 * 60 * 1000 // 10 minutes in milliseconds
    };

    // Phase 5: Schedule and Smart Redirect
    this.scheduleSettings = {
      enableScheduleMode: false,
      scheduleType: 'daily',
      dailySchedule: {},
      weeklySchedule: {},
      customSchedule: {},
      scheduleOverride: {
        isActive: false,
        endTime: null
      }
    };

    this.smartRedirects = {
      enableSmartRedirects: false,
      categoryRedirects: {
        'Social Media → Productivity': 'notion.so',
        'Entertainment → Learning': 'coursera.org',
        'Shopping → Finance': 'mint.com'
      },
      customRedirects: {}
    };

    this.init();
  }

  async init() {
    // Load saved settings and blocked sites
    await this.loadSettings();

    // Phase 4: Initialize content filtering
    this.initializeContentFiltering();

    // Set up event listeners
    this.setupEventListeners();

    // Initialize blocking rules
    await this.updateBlockingRules();

    console.log('FocusGuard Pro initialized with', this.blockedSites.size, 'blocked sites');
    console.log('Blocked sites:', Array.from(this.blockedSites));
    console.log('Adult content sites loaded:', this.adultContentSites.size);
    console.log('Extension enabled:', this.isEnabled);
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get([
        'blockedSites',
        'isEnabled',
        'settings',
        'temporaryUnblocks',
        'usageTracking',
        'dailyLimits',
        'focusMode',
        'masterPassword',
        'adultContentSites',
        'blockedKeywords',
        'contentFiltering',
        'protectionDisabling',
        'scheduleSettings',
        'smartRedirects'
      ]);

      this.blockedSites = new Set(result.blockedSites || []);
      this.isEnabled = result.isEnabled !== false; // Default to true
      this.settings = result.settings || this.getDefaultSettings();
      this.temporaryUnblocks = new Map(result.temporaryUnblocks || []);
      this.usageTracking = new Map(result.usageTracking || []);
      this.dailyLimits = new Map(result.dailyLimits || []);
      this.focusMode = { ...this.focusMode, ...(result.focusMode || {}) };
      this.masterPassword = result.masterPassword || null;

      // Phase 4: Load content filtering settings
      this.adultContentSites = new Set(result.adultContentSites || []);
      this.blockedPhrases = new Set(result.blockedPhrases || []);
      this.contentFiltering = { ...this.contentFiltering, ...(result.contentFiltering || {}) };
      this.protectionDisabling = { ...this.protectionDisabling, ...(result.protectionDisabling || {}) };

      // Phase 5: Load schedule and smart redirect settings
      this.scheduleSettings = { ...this.scheduleSettings, ...(result.scheduleSettings || {}) };
      this.smartRedirects = { ...this.smartRedirects, ...(result.smartRedirects || {}) };

      // Clean up expired temporary unblocks
      this.cleanupExpiredUnblocks();

      // Phase 4: Clean up expired protection disables
      this.cleanupExpiredProtectionDisables();

      // Phase 5: Clean up expired schedule overrides
      this.cleanupExpiredScheduleOverrides();

      // Reset daily usage if it's a new day
      this.resetDailyUsageIfNeeded();

      console.log('Settings loaded:', {
        blockedSites: Array.from(this.blockedSites),
        isEnabled: this.isEnabled,
        focusMode: this.focusMode
      });
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  getDefaultSettings() {
    return {
      defaultBlockDuration: '5days',
      showNotifications: true,
      workInIncognito: true,
      enableTimeTracking: true,
      enableTimeLimits: false,
      defaultDailyLimit: 120, // minutes
      focusMode: {
        workDuration: 25,
        breakDuration: 5,
        autoStartBreaks: true
      },
      passwordProtection: false,
      // Phase 4: Content filtering defaults
      contentFiltering: {
        enableAdultContentBlocking: true,
        enablePhraseBlocking: true,
        strictMode: false,
        customPhrases: []
      },
      // Phase 5: Schedule and redirect defaults
      enableScheduleMode: false,
      scheduleType: 'daily',
      enableSmartRedirects: false
    };
  }

  // Phase 4: Content Filtering Initialization
  initializeContentFiltering() {
    // Initialize adult content database
    this.loadAdultContentDatabase();

    // Load user-defined blocked phrases (no defaults)
    this.loadUserBlockedPhrases();
  }

  loadAdultContentDatabase() {
    // Comprehensive database of known adult content domains
    const adultSites = [
      // Major adult video sites
      'pornhub.com', 'xvideos.com', 'xnxx.com', 'redtube.com', 'youporn.com',
      'brazzers.com', 'spankbang.com', 'youjizz.com', 'porntube.com', 'tube8.com',
      'tnaflix.com', 'beeg.com', 'porn.com', 'xhamster.com', 'sex.com',

      // Hentai and anime adult content
      'hentaifox.com', 'hentaihaven.xxx', 'javhd.com', 'e-hentai.org', 'nhentai.net',
      '8muses.com', 'hitomi.la', 'hanime.tv', 'rule34.xxx',

      // Live cam sites
      'watchmygf.me', 'myfreecams.com', 'chaturbate.com', 'livejasmin.com',
      'stripchat.com', 'cam4.com', 'camsoda.com', 'bongacams.com', 'imlive.com',
      'flirt4free.com', 'streamate.com', 'xhamsterlive.com', 'stripchatgirls.com',

      // Premium adult content platforms
      'onlyfans.com', 'fansly.com', 'manyvids.com', 'clips4sale.com',

      // Adult production companies
      'bangbros.com', 'realitykings.com', 'naughtyamerica.com', 'mofos.com',
      'teamskeet.com', 'nubiles.net', 'babes.com', 'digitalplayground.com',
      'metart.com', 'bang.com', 'twistys.com', 'evilangel.com', 'devilsfilm.com',
      'newsensations.com', 'puretaboo.com', 'aziani.com', 'pornpros.com',

      // Additional adult sites from research
      'rutube.ru', 'theporndude.com', 'redgifs.com', 'twidouga.net', 'thisvid.com',
      'pornzog.com', 'hqporner.com', 'pornpics.com', 'dlsite.com', 'daftsex.com',
      'eporner.com', 'literotica.com', 'porntrex.com', 'sunporno.com',
      'sniffies.com', 'listcrawler.eu', 'mydirtyhobby.com', 'porno.com',
      'adultfriendfinder.com', 'xxx.com', 'adultdvdtalk.com', 'pornoxo.com',
      'faphouse.com', 'missav.ws', 'dmm.co.jp', 'erome.com', 'noodlemagazine.com',
      'ixxx.com', 'xgroovy.com', 'qorno.com', 'love4porn.com', 'txnhh.com',

      // XNXX variants and mirrors
      'xnxx.tv', 'xnxx.es', 'xnxx.org', 'xnxx.co', 'xnxx.co.uk', 'xnxx.co.in',
      'xnxx.co.id', 'xnxx.co.nz', 'xnxx.co.za', 'xnxx.co.jp', 'xnxx.co.kr',
      'xnxx.co.th', 'xnxx.co.vn', 'xnxx.co.ph', 'xnxx.co.sg', 'xnxx.co.my',
      'xnxx.health',

      // XHamster variants
      'xhamster43.desi', 'xhamster.desi', 'xham.live',

      // XVideos variants
      'xvideos.es', 'xvv1deos.com',

      // PornHub variants
      'pornhub.org',

      // Image hosting with adult content
      'imagefap.com', 'motherless.com'
    ];

    // Add to adult content sites set
    adultSites.forEach(site => this.adultContentSites.add(site));

    console.log('Adult content database loaded with', this.adultContentSites.size, 'sites');
  }

  async loadUserBlockedPhrases() {
    // Load user-defined phrases and automatically add research-based default phrases
    try {
      const result = await chrome.storage.local.get(['blockedPhrases', 'defaultPhrasesAdded']);
      const userPhrases = result.blockedPhrases || [];
      const defaultPhrasesAdded = result.defaultPhrasesAdded || false;

      // Clear existing phrases
      this.blockedPhrases.clear();

      // Add user-defined phrases
      userPhrases.forEach(phrase => {
        if (phrase && phrase.trim()) {
          this.blockedPhrases.add(phrase.toLowerCase().trim());
        }
      });

      // Automatically add default research-based phrases if not already added
      if (!defaultPhrasesAdded) {
        const defaultPhrases = this.getDefaultBlockedPhrases();
        let addedCount = 0;

        defaultPhrases.forEach(phrase => {
          if (!this.blockedPhrases.has(phrase.toLowerCase())) {
            this.blockedPhrases.add(phrase.toLowerCase());
            addedCount++;
          }
        });

        if (addedCount > 0) {
          // Save updated phrases and mark defaults as added
          const allPhrases = Array.from(this.blockedPhrases);
          await chrome.storage.local.set({
            blockedPhrases: allPhrases,
            defaultPhrasesAdded: true
          });

          console.log(`Automatically added ${addedCount} research-based default phrases`);
        }
      }

      console.log('Total blocked phrases loaded:', this.blockedPhrases.size, 'phrases');
    } catch (error) {
      console.error('Error loading blocked phrases:', error);
    }
  }

  async addUserPhrase(phrase) {
    // Add a user-defined phrase to the blocked list
    if (!phrase || !phrase.trim()) {
      return { success: false, error: 'Phrase cannot be empty' };
    }

    const normalizedPhrase = phrase.toLowerCase().trim();

    // Validate phrase (must be at least 2 words to prevent false positives)
    if (normalizedPhrase.split(' ').length < 2) {
      return { success: false, error: 'Phrase must contain at least 2 words to prevent false positives' };
    }

    if (this.blockedPhrases.has(normalizedPhrase)) {
      return { success: false, error: 'Phrase already exists' };
    }

    try {
      this.blockedPhrases.add(normalizedPhrase);

      // Save to storage
      const phrasesArray = Array.from(this.blockedPhrases);
      await chrome.storage.local.set({ blockedPhrases: phrasesArray });

      console.log('Added user phrase:', normalizedPhrase);
      return { success: true };
    } catch (error) {
      console.error('Error adding user phrase:', error);
      return { success: false, error: 'Failed to save phrase' };
    }
  }

  async removeUserPhrase(phrase) {
    // Remove a user-defined phrase from the blocked list
    const normalizedPhrase = phrase.toLowerCase().trim();

    if (!this.blockedPhrases.has(normalizedPhrase)) {
      return { success: false, error: 'Phrase not found' };
    }

    try {
      this.blockedPhrases.delete(normalizedPhrase);

      // Save to storage
      const phrasesArray = Array.from(this.blockedPhrases);
      await chrome.storage.local.set({ blockedPhrases: phrasesArray });

      console.log('Removed user phrase:', normalizedPhrase);
      return { success: true };
    } catch (error) {
      console.error('Error removing user phrase:', error);
      return { success: false, error: 'Failed to remove phrase' };
    }
  }

  getDefaultBlockedPhrases() {
    // Research-based phrases automatically added - converted to multi-word format to prevent false positives
    return [
      // Explicit Content
      'xxx video', 'xxx movies', 'hardcore sex', 'hardcore porn', 'softcore porn',
      'erotic videos', 'nude pics', 'naked girls', 'naked boys', 'porn video',
      'porn movie', 'porn clip', 'porn site', 'adult video', 'free porn',

      // Sexual Acts
      'anal sex', 'oral sex', 'blowjob video', 'handjob video', 'cumshot porn',
      'facial porn', 'orgy video', 'threesome porn', 'lesbian sex', 'webcam sex',
      'sex video', 'sex cam', 'sex chat', 'lesbian porn', 'gay porn', 'trans porn',

      // Fetish Content
      'fetish porn', 'bdsm videos', 'taboo porn', 'barely legal', 'incest porn',
      'stepmom porn', 'stepsister porn', 'slut videos', 'whore videos', 'escort porn',

      // Live Cam Content
      'live sex cams', 'cam girl site', 'camgirl sites', 'live cam', 'cam girl',

      // Premium Content
      'onlyfans leaks', 'fansly leaks', 'amateur porn', 'escort service',

      // Adult Categories
      'milf porn', 'teen porn', 'big tits', 'deepthroat videos', 'dildo videos',
      'gangbang videos', 'hentai videos', 'nude photos', 'porn star', 'pussy videos',
      'shemale porn', 'titfuck videos', 'tranny porn',

      // Additional from research
      'porn videos', 'porno videos', 'pornography videos', 'free sex videos',
      'adult webcam', 'sex toys', 'masturbation videos', 'orgasm videos'
    ];
  }

  // Method removed - phrases are now automatically added during initialization

  // Phase 2: Timer and Usage Tracking Methods
  cleanupExpiredUnblocks() {
    const now = Date.now();
    for (const [domain, endTime] of this.temporaryUnblocks) {
      if (now > endTime) {
        this.temporaryUnblocks.delete(domain);
      }
    }
    this.saveSettings();
  }

  // Phase 4: Clean up expired protection disables
  cleanupExpiredProtectionDisables() {
    const now = Date.now();

    // Check if temporary disable has expired
    if (this.protectionDisabling.temporaryDisableEndTime && now > this.protectionDisabling.temporaryDisableEndTime) {
      // Re-enable protection
      this.contentFiltering.enableAdultContentBlocking = true;
      this.contentFiltering.enablePhraseBlocking = true;
      this.protectionDisabling.temporaryDisableEndTime = null;
      this.protectionDisabling.isCountingDown = false;
      this.protectionDisabling.countdownStartTime = null;

      console.log('Adult content protection automatically re-enabled after temporary disable');

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Protection Re-enabled',
          message: 'Adult content protection has been automatically re-enabled for your safety.'
        });
      }

      this.saveSettings();
    }
  }

  // Phase 5: Clean up expired schedule overrides
  cleanupExpiredScheduleOverrides() {
    const now = Date.now();

    if (this.scheduleSettings.scheduleOverride.isActive &&
      this.scheduleSettings.scheduleOverride.endTime &&
      now > this.scheduleSettings.scheduleOverride.endTime) {

      // Override has expired
      this.scheduleSettings.scheduleOverride.isActive = false;
      this.scheduleSettings.scheduleOverride.endTime = null;

      console.log('Schedule override automatically expired');

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Schedule Override Expired',
          message: 'Schedule restrictions have been automatically restored.'
        });
      }

      this.saveSettings();
    }
  }

  resetDailyUsageIfNeeded() {
    const today = new Date().toDateString();
    const lastReset = this.settings.lastUsageReset;

    if (lastReset !== today) {
      this.usageTracking.clear();
      this.settings.lastUsageReset = today;
      this.saveSettings();
    }
  }

  async trackUsage(domain, timeSpent) {
    if (!this.settings.enableTimeTracking) return;

    const currentUsage = this.usageTracking.get(domain) || 0;
    const newUsage = currentUsage + timeSpent;
    this.usageTracking.set(domain, newUsage);

    // Check if daily limit exceeded
    const limit = this.dailyLimits.get(domain) || this.settings.defaultDailyLimit;
    if (this.settings.enableTimeLimits && newUsage >= limit) {
      // Add to blocked sites temporarily
      this.blockedSites.add(domain);

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Daily Limit Reached',
          message: `You've reached your daily limit for ${domain}`
        });
      }
    }

    await this.saveSettings();
  }

  // Phase 2: Focus Mode Methods
  async startFocusSession() {
    this.focusMode.isActive = true;
    this.focusMode.isBreak = false;
    this.focusMode.sessionStartTime = Date.now();

    // Set alarm for work session end
    chrome.alarms.create('focusSessionEnd', {
      delayInMinutes: this.focusMode.sessionDuration
    });

    // Block all sites during focus mode (optional enhancement)
    this.focusModeBlocking = true;

    await this.saveSettings();

    if (this.settings.showNotifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Focus Session Started',
        message: `${this.focusMode.sessionDuration} minute work session started`
      });
    }
  }

  async startBreak() {
    this.focusMode.isBreak = true;
    this.focusMode.sessionStartTime = Date.now();
    this.focusMode.sessionsCompleted++;

    // Set alarm for break end
    chrome.alarms.create('breakEnd', {
      delayInMinutes: this.focusMode.breakDuration
    });

    // Unblock sites during break
    this.focusModeBlocking = false;

    await this.saveSettings();

    if (this.settings.showNotifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Break Time!',
        message: `${this.focusMode.breakDuration} minute break started`
      });
    }
  }

  async stopFocusMode() {
    this.focusMode.isActive = false;
    this.focusMode.isBreak = false;
    this.focusMode.sessionStartTime = null;
    this.focusModeBlocking = false;

    // Clear alarms
    chrome.alarms.clear('focusSessionEnd');
    chrome.alarms.clear('breakEnd');

    await this.saveSettings();

    if (this.settings.showNotifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Focus Mode Stopped',
        message: `Completed ${this.focusMode.sessionsCompleted} sessions`
      });
    }
  }

  setupEventListeners() {
    // Listen for tab updates to check for blocked sites
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'loading' && tab.url) {
        this.checkAndBlockSite(tabId, tab.url);
      }
    });

    // Listen for navigation events
    chrome.webNavigation.onBeforeNavigate.addListener((details) => {
      if (details.frameId === 0) { // Main frame only
        this.checkAndBlockSite(details.tabId, details.url);
      }
    });

    // Listen for messages from popup and content scripts
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.handleFirstInstall();
      }
    });

    // Phase 2: Handle alarms for focus mode
    chrome.alarms.onAlarm.addListener((alarm) => {
      this.handleAlarm(alarm);
    });
  }

  // Phase 3: Security and Password Methods
  async setMasterPassword(password) {
    // Hash the password for security
    const hashedPassword = await this.hashPassword(password);
    this.masterPassword = hashedPassword;
    await this.saveSettings();
    return true;
  }

  async verifyMasterPassword(password) {
    if (!this.masterPassword) return false;
    const hashedInput = await this.hashPassword(password);
    return hashedInput === this.masterPassword;
  }

  async hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'focusguard_salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  generateOTP() {
    // Generate 6-digit OTP
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async createTemporaryUnblock(domain, durationMinutes, otp = null) {
    const endTime = Date.now() + (durationMinutes * 60 * 1000);
    this.temporaryUnblocks.set(domain, endTime);

    // Store OTP if provided
    if (otp) {
      this.activeOTPs = this.activeOTPs || new Map();
      this.activeOTPs.set(otp, { domain, endTime });

      // Clear OTP after 10 minutes
      setTimeout(() => {
        this.activeOTPs.delete(otp);
      }, 10 * 60 * 1000);
    }

    await this.saveSettings();

    if (this.settings.showNotifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Temporary Unblock',
        message: `${domain} unblocked for ${durationMinutes} minutes`
      });
    }
  }

  async verifyOTP(otp, domain) {
    if (!this.activeOTPs || !this.activeOTPs.has(otp)) {
      return false;
    }

    const otpData = this.activeOTPs.get(otp);
    if (otpData.domain !== domain || Date.now() > otpData.endTime) {
      this.activeOTPs.delete(otp);
      return false;
    }

    return true;
  }

  async handleAlarm(alarm) {
    switch (alarm.name) {
      case 'focusSessionEnd':
        if (this.settings.focusMode.autoStartBreaks) {
          await this.startBreak();
        } else {
          await this.stopFocusMode();
        }
        break;

      case 'breakEnd':
        await this.stopFocusMode();
        break;
    }
  }

  async handleMessage(request, _sender, sendResponse) {
    try {
      console.log('Background received message:', request.action);

      switch (request.action) {
        // Phase 1 actions
        case 'addBlockedSite':
          await this.addBlockedSite(request.url);
          sendResponse({ success: true });
          break;

        case 'removeBlockedSite':
          await this.removeBlockedSite(request.url);
          sendResponse({ success: true });
          break;

        case 'getBlockedSites':
          sendResponse({
            sites: Array.from(this.blockedSites),
            isEnabled: this.isEnabled,
            focusMode: this.focusMode,
            usageTracking: Object.fromEntries(this.usageTracking),
            temporaryUnblocks: Object.fromEntries(this.temporaryUnblocks)
          });
          break;

        case 'toggleBlocking':
          await this.toggleBlocking();
          sendResponse({ success: true, isEnabled: this.isEnabled });
          break;

        case 'checkSiteStatus':
          const isBlocked = this.isSiteBlocked(request.url);
          sendResponse({ isBlocked });
          break;

        // Phase 2 actions - Focus Mode
        case 'startFocusSession':
          console.log('Starting focus session...');
          await this.startFocusSession();
          sendResponse({ success: true, focusMode: this.focusMode });
          break;

        case 'startBreak':
          console.log('Starting break...');
          await this.startBreak();
          sendResponse({ success: true, focusMode: this.focusMode });
          break;

        case 'stopFocusMode':
          console.log('Stopping focus mode...');
          await this.stopFocusMode();
          sendResponse({ success: true, focusMode: this.focusMode });
          break;

        case 'getFocusStatus':
          console.log('Getting focus status:', this.focusMode);
          sendResponse({ success: true, focusMode: this.focusMode });
          break;

        // Phase 2 actions - Time Limits
        case 'setTimeLimit':
          this.dailyLimits.set(request.domain, request.limitMinutes);
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'getUsageStats':
          sendResponse({
            usage: Object.fromEntries(this.usageTracking),
            limits: Object.fromEntries(this.dailyLimits)
          });
          break;

        case 'updateTimeSpent':
          await this.trackUsage(request.domain, request.timeSpent);
          sendResponse({ success: true });
          break;

        // Phase 3 actions - Security
        case 'setMasterPassword':
          const passwordSet = await this.setMasterPassword(request.password);
          sendResponse({ success: passwordSet });
          break;

        case 'verifyMasterPassword':
          const isValid = await this.verifyMasterPassword(request.password);
          sendResponse({ valid: isValid });
          break;

        case 'generateOTP':
          const otp = this.generateOTP();
          sendResponse({ otp });
          break;

        case 'createTemporaryUnblock':
          await this.createTemporaryUnblock(request.domain, request.durationMinutes, request.otp);
          sendResponse({ success: true });
          break;

        case 'verifyOTP':
          const otpValid = await this.verifyOTP(request.otp, request.domain);
          sendResponse({ valid: otpValid });
          break;

        case 'getSecurityStatus':
          sendResponse({
            hasPassword: !!this.masterPassword,
            passwordProtection: this.settings.passwordProtection
          });
          break;

        // Phase 4 actions - Content Filtering (Updated for phrase-based system)
        case 'updateContentFiltering':
          this.contentFiltering = { ...this.contentFiltering, ...request.settings };
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'getContentFilteringStatus':
          sendResponse({
            contentFiltering: this.contentFiltering,
            adultSitesCount: this.adultContentSites.size,
            phrasesCount: this.blockedPhrases.size
          });
          break;

        case 'addCustomPhrase':
          const addResult = await this.addUserPhrase(request.phrase);
          sendResponse(addResult);
          break;

        case 'removeCustomPhrase':
          const removeResult = await this.removeUserPhrase(request.phrase);
          sendResponse(removeResult);
          break;

        case 'getBlockedPhrases':
          sendResponse({
            phrases: Array.from(this.blockedPhrases)
          });
          break;

        // Note: Suggested phrases functionality removed - phrases are now automatically added

        case 'getBlockedDomains':
          sendResponse({
            domains: Array.from(this.adultContentSites),
            count: this.adultContentSites.size
          });
          break;

        // Phase 4: Protection disabling with countdown
        case 'startProtectionDisableCountdown':
          this.protectionDisabling.isCountingDown = true;
          this.protectionDisabling.countdownStartTime = Date.now();
          await this.saveSettings();
          sendResponse({ success: true, countdownStartTime: this.protectionDisabling.countdownStartTime });
          break;

        case 'cancelProtectionDisableCountdown':
          this.protectionDisabling.isCountingDown = false;
          this.protectionDisabling.countdownStartTime = null;
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'confirmProtectionDisable':
          // Check if countdown has completed (1 hour)
          const now = Date.now();
          const countdownElapsed = now - (this.protectionDisabling.countdownStartTime || 0);

          if (countdownElapsed >= this.protectionDisabling.countdownDuration) {
            // Temporarily disable protection for 10 minutes
            this.protectionDisabling.temporaryDisableEndTime = now + this.protectionDisabling.temporaryDisableDuration;
            this.protectionDisabling.isCountingDown = false;
            this.protectionDisabling.countdownStartTime = null;

            await this.saveSettings();

            // Show warning notification
            if (this.settings.showNotifications) {
              chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'Protection Temporarily Disabled',
                message: 'Adult content protection disabled for 10 minutes. It will automatically re-enable.'
              });
            }

            sendResponse({
              success: true,
              temporaryDisableEndTime: this.protectionDisabling.temporaryDisableEndTime
            });
          } else {
            sendResponse({
              success: false,
              error: 'Countdown not completed',
              remainingTime: this.protectionDisabling.countdownDuration - countdownElapsed
            });
          }
          break;

        case 'getProtectionDisableStatus':
          sendResponse({
            protectionDisabling: this.protectionDisabling,
            isTemporarilyDisabled: this.protectionDisabling.temporaryDisableEndTime &&
              Date.now() < this.protectionDisabling.temporaryDisableEndTime
          });
          break;

        // Phase 5 actions - Schedule Management
        case 'getScheduleSettings':
          sendResponse({
            scheduleSettings: this.scheduleSettings
          });
          break;

        case 'updateScheduleSettings':
          this.scheduleSettings = { ...this.scheduleSettings, ...request.scheduleSettings };
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'startScheduleOverride':
          const overrideEndTime = Date.now() + (request.durationMinutes * 60 * 1000);
          this.scheduleSettings.scheduleOverride = {
            isActive: true,
            endTime: overrideEndTime
          };
          await this.saveSettings();
          sendResponse({ success: true, endTime: overrideEndTime });
          break;

        case 'cancelScheduleOverride':
          this.scheduleSettings.scheduleOverride = {
            isActive: false,
            endTime: null
          };
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        // Phase 5 actions - Smart Redirects
        case 'getSmartRedirects':
          sendResponse({
            smartRedirects: this.smartRedirects
          });
          break;

        case 'updateSmartRedirect':
          if (!this.smartRedirects.categoryRedirects) {
            this.smartRedirects.categoryRedirects = {};
          }
          this.smartRedirects.categoryRedirects[request.category] = request.redirectUrl;
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'addCustomRedirect':
          if (!this.smartRedirects.customRedirects) {
            this.smartRedirects.customRedirects = {};
          }
          this.smartRedirects.customRedirects[request.source] = request.target;
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'removeCustomRedirect':
          if (this.smartRedirects.customRedirects && this.smartRedirects.customRedirects[request.source]) {
            delete this.smartRedirects.customRedirects[request.source];
            await this.saveSettings();
            sendResponse({ success: true });
          } else {
            sendResponse({ success: false, error: 'Redirect not found' });
          }
          break;

        default:
          console.warn('Unknown action:', request.action);
          sendResponse({ error: 'Unknown action', action: request.action });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message, success: false });
    }
  }

  async addBlockedSite(url) {
    const domain = this.extractDomain(url);
    console.log('Adding blocked site:', url, '-> domain:', domain);

    if (domain) {
      this.blockedSites.add(domain);
      console.log('Blocked sites now:', Array.from(this.blockedSites));

      await this.saveSettings();
      await this.updateBlockingRules();

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'FocusGuard Pro',
          message: `${domain} has been blocked`
        });
      }

      console.log('Successfully added blocked site:', domain);
    } else {
      console.error('Could not extract domain from:', url);
    }
  }

  async removeBlockedSite(url) {
    const domain = this.extractDomain(url);
    if (domain && this.blockedSites.has(domain)) {
      this.blockedSites.delete(domain);
      await this.saveSettings();
      await this.updateBlockingRules();

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'FocusGuard Pro',
          message: `${domain} has been unblocked`
        });
      }
    }
  }

  async toggleBlocking() {
    this.isEnabled = !this.isEnabled;
    await this.saveSettings();
    await this.updateBlockingRules();
  }

  extractDomain(url) {
    try {
      // If URL doesn't have protocol, add one
      if (!url.includes('://')) {
        url = 'http://' + url;
      }

      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');
      console.log('Extracted domain:', domain, 'from URL:', url);
      return domain;
    } catch (error) {
      // If URL parsing fails, try to extract domain manually
      console.log('URL parsing failed, trying manual extraction for:', url);

      // Remove protocol if present
      let domain = url.replace(/^https?:\/\//, '');

      // Remove www. if present
      domain = domain.replace(/^www\./, '');

      // Remove path and query parameters
      domain = domain.split('/')[0].split('?')[0].split('#')[0];

      // Basic validation - should contain at least one dot
      if (domain.includes('.') && domain.length > 3) {
        console.log('Manually extracted domain:', domain);
        return domain;
      }

      console.error('Could not extract domain from:', url);
      return null;
    }
  }

  isSiteBlocked(url) {
    console.log('Checking if site is blocked:', url);
    console.log('Extension enabled:', this.isEnabled);
    console.log('Blocked sites:', Array.from(this.blockedSites));

    if (!this.isEnabled) {
      console.log('Extension disabled, not blocking');
      return false;
    }

    const domain = this.extractDomain(url);
    console.log('Extracted domain:', domain);

    if (!domain) {
      console.log('No domain extracted, not blocking');
      return false;
    }

    // Skip chrome:// and extension pages
    if (url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
      console.log('Chrome internal page, not blocking');
      return false;
    }

    // Phase 4: Check adult content blocking (cannot be overridden by temporary unblocks)
    if (this.contentFiltering.enableAdultContentBlocking && this.isAdultContent(domain, url)) {
      console.log('Adult content detected, blocking site');
      return true;
    }

    // Phase 5: Check schedule restrictions
    if (this.settings.enableScheduleMode && this.isOutsideScheduledHours()) {
      console.log('Outside scheduled hours, blocking site');
      return true;
    }

    // Check if temporarily unblocked
    if (this.temporaryUnblocks.has(domain)) {
      const unblockEndTime = this.temporaryUnblocks.get(domain);
      if (Date.now() < unblockEndTime) {
        console.log('Site temporarily unblocked');
        return false; // Site is temporarily unblocked
      } else {
        // Temporary unblock expired, remove it
        this.temporaryUnblocks.delete(domain);
        this.saveSettings();
        console.log('Temporary unblock expired');
      }
    }

    // Check focus mode blocking
    if (this.focusModeBlocking && this.focusMode.isActive && !this.focusMode.isBreak) {
      // During focus mode, block all sites except essential ones
      const essentialSites = ['localhost', 'chrome-extension', 'chrome://', 'about:'];
      if (!essentialSites.some(site => url.includes(site))) {
        console.log('Focus mode active, blocking site');
        return true;
      }
    }

    // Check exact match
    if (this.blockedSites.has(domain)) {
      console.log('Exact match found, blocking site');
      return true;
    }

    // Check subdomain match
    for (const blockedDomain of this.blockedSites) {
      if (domain.endsWith('.' + blockedDomain) || domain === blockedDomain) {
        console.log('Subdomain match found, blocking site');
        return true;
      }
    }

    console.log('Site not in blocked list, allowing');
    return false;
  }

  // Phase 4: Adult Content Detection
  isAdultContent(domain, url) {
    // Check if adult content protection is temporarily disabled
    if (this.protectionDisabling.temporaryDisableEndTime &&
      Date.now() < this.protectionDisabling.temporaryDisableEndTime) {
      return false; // Temporarily disabled
    }

    // Whitelist trusted financial and business domains
    const trustedDomains = [
      'paystack.com', 'stripe.com', 'paypal.com', 'square.com',
      'flutterwave.com', 'interswitch.com', 'remita.net',
      'bank.com', 'banking.com', 'finance.com', 'fintech.com',
      'visa.com', 'mastercard.com', 'americanexpress.com',
      'chase.com', 'wellsfargo.com', 'bankofamerica.com',
      'citibank.com', 'hsbc.com', 'barclays.com',
      'gtbank.com', 'zenithbank.com', 'accessbank.com',
      'firstbank.com', 'ubagroup.com', 'sterlingbank.com'
    ];

    // Skip adult content detection for trusted domains
    if (trustedDomains.some(trusted => domain.includes(trusted))) {
      return false;
    }

    // Check against adult content database
    if (this.adultContentSites.has(domain)) {
      return true;
    }

    // Removed automatic pattern matching and URL keyword scanning
    // Only explicit domain database blocking remains active
    // This prevents false positives on legitimate sites like "adultlearning.com"

    return false;
  }

  // Phase 4: Keyword Blocking for Search Engines - DISABLED per user request
  containsBlockedKeywords(searchQuery) {
    // Search query filtering has been disabled to prevent false positives
    // Only domain-based blocking remains active
    return false;
  }

  // Phase 4: Force safe search on search engines
  forceSafeSearch(url) {
    if (!this.contentFiltering.forceSafeSearch) {
      return url; // Safe search not enabled
    }

    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');

      // Apply safe search parameters for different search engines
      if (domain.includes('google.com')) {
        urlObj.searchParams.set('safe', 'strict');
      } else if (domain.includes('bing.com')) {
        urlObj.searchParams.set('adlt', 'strict');
      } else if (domain.includes('yahoo.com')) {
        urlObj.searchParams.set('vm', 'r');
      } else if (domain.includes('duckduckgo.com')) {
        urlObj.searchParams.set('kp', '1'); // Strict safe search
      } else if (domain.includes('yandex.com')) {
        urlObj.searchParams.set('family', 'yes');
      }

      return urlObj.toString();
    } catch (error) {
      console.error('Error applying safe search:', error);
      return url;
    }
  }

  // Phase 4: Check if URL is a search with blocked keywords - DISABLED per user request
  isBlockedSearch(url) {
    // Search query filtering has been disabled to prevent false positives
    // Only domain-based blocking remains active
    return false;
  }

  async checkAndBlockSite(tabId, url) {
    console.log('Checking site:', url, 'Tab ID:', tabId);

    // Phase 4: Apply safe search if enabled
    const safeSearchUrl = this.forceSafeSearch(url);
    if (safeSearchUrl !== url) {
      console.log('Applying safe search redirect:', url, '->', safeSearchUrl);
      try {
        await chrome.tabs.update(tabId, { url: safeSearchUrl });
        console.log('Successfully applied safe search');
        return;
      } catch (error) {
        console.error('Error applying safe search:', error);
      }
    }

    // Note: Search query filtering has been disabled per user request
    // Only domain-based blocking and safe search enforcement remain active

    if (this.isSiteBlocked(url)) {
      console.log('Site is blocked, checking for smart redirect:', url);

      // Phase 5: Check for smart redirect
      const redirectUrl = this.getSmartRedirectUrl(url);
      if (redirectUrl) {
        console.log('Smart redirect found, redirecting to:', redirectUrl);
        try {
          await chrome.tabs.update(tabId, { url: redirectUrl });
          console.log('Successfully applied smart redirect');
          return;
        } catch (error) {
          console.error('Error applying smart redirect:', error);
        }
      }

      // Redirect to blocked page
      const blockedPageUrl = chrome.runtime.getURL('blocked/blocked.html') +
        '?blocked=' + encodeURIComponent(url);

      try {
        await chrome.tabs.update(tabId, { url: blockedPageUrl });
        console.log('Successfully redirected to blocked page');
      } catch (error) {
        console.error('Error redirecting to blocked page:', error);
      }
    } else {
      console.log('Site is not blocked:', url);
    }
  }

  async updateBlockingRules() {
    // This will be used for declarativeNetRequest rules in future phases
    // For now, we handle blocking through tab redirection
    console.log('Blocking rules updated for', this.blockedSites.size, 'sites');
  }

  async saveSettings() {
    try {
      await chrome.storage.local.set({
        blockedSites: Array.from(this.blockedSites),
        isEnabled: this.isEnabled,
        settings: this.settings,
        temporaryUnblocks: Array.from(this.temporaryUnblocks),
        usageTracking: Array.from(this.usageTracking),
        dailyLimits: Array.from(this.dailyLimits),
        focusMode: this.focusMode,
        masterPassword: this.masterPassword,
        // Phase 4: Content filtering data
        adultContentSites: Array.from(this.adultContentSites),
        blockedPhrases: Array.from(this.blockedPhrases),
        contentFiltering: this.contentFiltering,
        protectionDisabling: this.protectionDisabling,
        // Phase 5: Schedule and redirect data
        scheduleSettings: this.scheduleSettings,
        smartRedirects: this.smartRedirects
      });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  async handleFirstInstall() {
    // Set up default settings and show welcome notification
    await this.saveSettings();

    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'Welcome to FocusGuard Pro!',
      message: 'Click the extension icon to start blocking distracting websites.'
    });

    // Open options page
    chrome.tabs.create({ url: chrome.runtime.getURL('options/options.html') });
  }

  // Phase 5: Schedule Management Methods
  isOutsideScheduledHours() {
    // Check if schedule override is active
    if (this.scheduleSettings.scheduleOverride.isActive) {
      return false; // Override allows access
    }

    const now = new Date();
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

    // Check daily schedule
    if (this.scheduleSettings.scheduleType === 'daily' && this.scheduleSettings.dailySchedule) {
      const daySchedule = this.scheduleSettings.dailySchedule[currentDay];

      if (!daySchedule || !daySchedule.enabled) {
        return true; // Day is disabled, so block access
      }

      const startTime = daySchedule.start || '09:00';
      const endTime = daySchedule.end || '17:00';

      // Check if current time is within allowed hours
      if (currentTime < startTime || currentTime > endTime) {
        return true; // Outside allowed hours
      }
    }

    return false; // Within allowed hours
  }

  // Phase 5: Smart Redirect Methods
  getSmartRedirectUrl(blockedUrl) {
    if (!this.settings.enableSmartRedirects) {
      return null;
    }

    const domain = this.extractDomain(blockedUrl);
    if (!domain) return null;

    // Check custom redirects first
    if (this.smartRedirects.customRedirects && this.smartRedirects.customRedirects[domain]) {
      let redirectUrl = this.smartRedirects.customRedirects[domain];
      // Ensure URL has protocol
      if (!redirectUrl.includes('://')) {
        redirectUrl = 'https://' + redirectUrl;
      }
      return redirectUrl;
    }

    // Check category-based redirects
    const category = this.getSiteCategory(domain);
    const categoryRedirect = this.getCategoryRedirect(category);

    if (categoryRedirect) {
      let redirectUrl = categoryRedirect;
      // Ensure URL has protocol
      if (!redirectUrl.includes('://')) {
        redirectUrl = 'https://' + redirectUrl;
      }
      return redirectUrl;
    }

    return null;
  }

  getSiteCategory(domain) {
    // Social media sites
    const socialMediaSites = [
      'facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com',
      'snapchat.com', 'tiktok.com', 'reddit.com', 'pinterest.com'
    ];

    // Entertainment sites
    const entertainmentSites = [
      'youtube.com', 'netflix.com', 'hulu.com', 'twitch.tv',
      'spotify.com', 'soundcloud.com', 'vimeo.com'
    ];

    // Shopping sites
    const shoppingSites = [
      'amazon.com', 'ebay.com', 'etsy.com', 'walmart.com',
      'target.com', 'bestbuy.com', 'alibaba.com'
    ];

    if (socialMediaSites.some(site => domain.includes(site))) {
      return 'Social Media → Productivity';
    }

    if (entertainmentSites.some(site => domain.includes(site))) {
      return 'Entertainment → Learning';
    }

    if (shoppingSites.some(site => domain.includes(site))) {
      return 'Shopping → Finance';
    }

    return 'Other';
  }

  getCategoryRedirect(category) {
    if (this.smartRedirects.categoryRedirects && this.smartRedirects.categoryRedirects[category]) {
      return this.smartRedirects.categoryRedirects[category];
    }
    return null;
  }
}

// Initialize the background service
const focusGuard = new FocusGuardBackground();
